package com.zjhh.sat.jxsrcxfx.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.zjhh.comm.exception.BizException;
import com.zjhh.comm.vo.SingleSelectVo;
import com.zjhh.comm.vo.SingleSelectedVo;
import com.zjhh.sat.jxsrcxfx.service.InitMenuService;
import com.zjhh.user.enume.AreaGradeEnum;
import com.zjhh.user.service.impl.UserSession;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/23 15:09
 */
@Service
public class InitMenuServiceImpl implements InitMenuService {

    @Resource
    private DmTzjsCzfpMapper dmTzjsCzfpMapper;

    @Resource
    private UserSession userSession;

    @Override
    public List<SingleSelectVo> listXzqh() {
        return dmTzjsCzfpMapper.listXzqh();
    }

    @Override
    public List<SingleSelectVo> listJdxz(String xzqh) {
        return dmTzjsCzfpMapper.listJdxz(xzqh);
    }

    @Override
    public SingleSelectedVo listAuthXzqh() {
        SingleSelectedVo vo = new SingleSelectedVo();
        List<SingleSelectVo> list;
        String selectKey = null;
        Integer level = userSession.getSessionLoginVo().getOrganize().getOrganLevel();
        String organizeCode = userSession.getOrganizeCode();
        AreaGradeEnum areaGradeEnum = AreaGradeEnum.getByValue(level);
        if (areaGradeEnum == null) {
            throw new BizException("用户行政区划层级错误！");
        }
        switch (areaGradeEnum) {
            case CENTRAL:
            case PROVINCE:
            case PLAN_CITY:
            case CITY:
                list = listXzqh();
                break;
            case DISTRICT:
                list = dmTzjsCzfpMapper.listByJdxzDm(organizeCode);
                if (CollUtil.isNotEmpty(list)) {
                    selectKey = list.getFirst().getCode().toString();
                }
                break;
            case VILLAGE:
            default:
                list = dmTzjsCzfpMapper.listJdUserXzqh(organizeCode);
                if (CollUtil.isNotEmpty(list)) {
                    selectKey = list.getFirst().getCode().toString();
                }
                break;
        }
        vo.setSelectedKey(selectKey);
        vo.setSingleSelects(list);
        return vo;
    }

    @Override
    public SingleSelectedVo listAuthJdxz(String xzqh) {
        SingleSelectedVo vo = new SingleSelectedVo();
        List<SingleSelectVo> list;
        String selectKey = null;
        Integer level = userSession.getSessionLoginVo().getOrganize().getOrganLevel();
        String organizeCode = userSession.getOrganizeCode();
        AreaGradeEnum areaGradeEnum = AreaGradeEnum.getByValue(level);
        if (areaGradeEnum == null) {
            throw new BizException("用户行政区划层级错误！");
        }
        switch (areaGradeEnum) {
            case CENTRAL:
            case PROVINCE:
            case PLAN_CITY:
            case CITY:
            case DISTRICT:
                list = listJdxz(xzqh);
                break;
            case VILLAGE:
            default:
                list = dmTzjsCzfpMapper.listByJdxzDm(organizeCode);
                if (CollUtil.isNotEmpty(list)) {
                    selectKey = list.getFirst().getCode().toString();
                }
                break;
        }
        vo.setSelectedKey(selectKey);
        vo.setSingleSelects(list);
        return vo;
    }

    @Override
    public List<SingleSelectVo> listJdxz() {
        return listJdxz(userSession.getDefaultAreaCode());
    }
}
